'use client'

import { useCallback, useEffect, useState } from 'react'
import Particles from '@tsparticles/react'
import { loadSlim } from '@tsparticles/slim'
import type { Engine, ISourceOptions } from '@tsparticles/engine'

interface ParticlesAbsorberProps {
  className?: string
  particleCount?: number
}

export default function ParticlesAbsorber({
  className = '',
  particleCount = 80
}: ParticlesAbsorberProps) {
  const [init, setInit] = useState(false)

  // 初始化 particles engine
  const particlesInit = useCallback(async (engine: Engine) => {
    await loadSlim(engine)
  }, [])

  const particlesLoaded = useCallback(async () => {
    setInit(true)
  }, [])

  const options: ISourceOptions = {
    background: {
      color: {
        value: 'transparent',
      },
    },
    fpsLimit: 120,
    interactivity: {
      events: {
        onClick: {
          enable: true,
          mode: 'push',
        },
        onHover: {
          enable: true,
          mode: 'attract',
        },
        resize: true,
      },
      modes: {
        push: {
          quantity: 4,
        },
        attract: {
          distance: 200,
          duration: 0.4,
          factor: 5,
        },
      },
    },
    particles: {
      color: {
        value: ['#00ffff', '#0080ff', '#40e0d0', '#87ceeb', '#ffffff'],
      },
      links: {
        color: '#00ffff',
        distance: 150,
        enable: true,
        opacity: 0.2,
        width: 1,
      },
      move: {
        direction: 'none',
        enable: true,
        outModes: {
          default: 'out',
        },
        random: true,
        speed: 1.5,
        straight: false,
      },
      number: {
        density: {
          enable: true,
          area: 800,
        },
        value: particleCount,
      },
      opacity: {
        value: 0.6,
        random: true,
        animation: {
          enable: true,
          speed: 1,
          minimumValue: 0.1,
          sync: false,
        },
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: { min: 1, max: 4 },
        random: true,
        animation: {
          enable: true,
          speed: 2,
          minimumValue: 0.5,
          sync: false,
        },
      },
    },
    // 模拟 absorber 效果的自定义配置
    emitters: {
      direction: 'none',
      life: {
        count: 0,
        duration: 0.1,
        delay: 0.1,
      },
      rate: {
        delay: 0.1,
        quantity: 1,
      },
      size: {
        width: 0,
        height: 0,
      },
      position: {
        x: 20,
        y: 30,
      },
    },
    detectRetina: true,
  }

  return (
    <div className={`absolute inset-0 ${className}`}>
      <Particles
        id="particles-absorber"
        init={particlesInit}
        loaded={particlesLoaded}
        options={options}
        className="w-full h-full"
      />
    </div>
  )
}
